from fastapi import APIRouter, Request, Depends, HTTPException
from pydantic import BaseModel
from typing import Optional, List, Dict, Any
import stripe
import os
from firebase_admin import firestore
from firebase.config import get_db
from auth.deps import require_role
import logging
from datetime import datetime

router = APIRouter()
db = get_db()

stripe.api_key = os.environ.get("STRIPE_SECRET_KEY")
DOMAIN = os.environ.get("STRIPE_DOMAIN")  # e.g. https://admesh.com

logger = logging.getLogger("stripe_webhook")
logging.basicConfig(level=logging.INFO)

class CheckoutRequest(BaseModel):
    # All monetary values must be integers in cents
    amount: int  # Total amount in cents (including fees)
    credit_amount: int = None  # Actual credit amount in cents (without fees)

@router.post("/create-checkout-session")
async def create_checkout_session(
    data: CheckoutRequest,
    user=Depends(require_role("brand"))
):
    # Validate the amount (in cents)
    if data.amount < 100:  # 100 cents = $1.00
        raise HTTPException(status_code=400, detail="Minimum amount is $1.00 (100 cents)")

    try:
        brand_id = user["uid"]

        # Get brand information to retrieve email
        brand_ref = db.collection("brands").document(brand_id)
        brand_doc = brand_ref.get()

        if not brand_doc.exists:
            raise HTTPException(status_code=404, detail="Brand not found")

        brand_data = brand_doc.to_dict()
        brand_email = brand_data.get("email") or user.get("email")

        if not brand_email:
            raise HTTPException(status_code=400, detail="Brand email not found")

        # Create or retrieve Stripe customer
        customer = None
        stripe_customer_id = brand_data.get("stripe_customer_id")

        if stripe_customer_id:
            try:
                # Try to retrieve existing customer
                customer = stripe.Customer.retrieve(stripe_customer_id)
            except stripe.error.InvalidRequestError:
                # Customer doesn't exist, we'll create a new one
                stripe_customer_id = None

        if not stripe_customer_id:
            # Create new Stripe customer
            customer = stripe.Customer.create(
                email=brand_email,
                metadata={
                    "brand_id": brand_id,
                    "admesh_customer": "true"
                }
            )

            # Save the Stripe customer ID to the brand document
            brand_ref.update({
                "stripe_customer_id": customer.id,
                "updated_at": firestore.SERVER_TIMESTAMP
            })

        session = stripe.checkout.Session.create(
            payment_method_types=["card"],
            customer=customer.id,
            line_items=[{
                "price_data": {
                    "currency": "usd",
                    "unit_amount": data.amount,  # Already in cents
                    "product_data": {
                        "name": "AdMesh Wallet Top-up",
                    },
                },
                "quantity": 1,
            }],
            mode="payment",
            success_url=f"{DOMAIN}/dashboard/brand/billing?success=true",
            cancel_url=f"{DOMAIN}/dashboard/brand/billing?canceled=true",
            metadata={
                "brand_id": brand_id,
                "amount": str(data.credit_amount or data.amount)
            },
            payment_intent_data={
                "metadata": {
                    "brand_id": brand_id,
                    "amount": str(data.credit_amount or data.amount)
                },
                "setup_future_usage": "on_session"  # Save payment method for future use
            }
        )
        return { "session_url": session.url }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/webhook")
async def stripe_webhook(request: Request):
    payload = await request.body()
    sig_header = request.headers.get("stripe-signature")
    endpoint_secret = os.environ.get("STRIPE_WEBHOOK_SECRET")

    logger.info("Received webhook request")

    try:
        event = stripe.Webhook.construct_event(payload, sig_header, endpoint_secret)
        logger.info(f"Webhook event type: {event['type']}")
    except stripe.error.SignatureVerificationError:
        logger.error("Invalid signature for webhook")
        raise HTTPException(status_code=400, detail="Invalid signature")

    # Store the event data first
    await _store_stripe_event(event)

    # Handle one-time payment checkout completion
    if event["type"] == "checkout.session.completed":
        session = event["data"]["object"]
        metadata = session.get("metadata", {})
        logger.info(f"Session metadata: {metadata}")

        # Handle regular one-time payment
        await _handle_payment_checkout(session)

    # Handle charge updates (when payment is actually processed)
    elif event["type"] == "charge.updated":
        charge = event["data"]["object"]
        logger.info(f"Charge updated: {charge['id']}, status: {charge['status']}")

        # Handle successful charge
        if charge["status"] == "succeeded":
            await _handle_charge_succeeded(charge, event)

    # Handle payment intent events
    elif event["type"] in ["payment_intent.created", "payment_intent.succeeded", "payment_intent.payment_failed"]:
        payment_intent = event["data"]["object"]
        logger.info(f"Payment intent {event['type']}: {payment_intent['id']}")

    # Handle charge events
    elif event["type"] in ["charge.succeeded", "charge.failed"]:
        charge = event["data"]["object"]
        logger.info(f"Charge {event['type']}: {charge['id']}")

    return {"status": "success"}

# Helper function to store Stripe events by transaction
async def _store_stripe_event(event):
    """Store Stripe event data organized by transaction ID"""
    try:
        event_type = event["type"]
        event_id = event["id"]
        event_data = event["data"]["object"]

        # Extract brand_id and transaction_id from different event types
        brand_id = None
        transaction_id = None

        # Try to get brand_id from metadata
        if "metadata" in event_data and event_data["metadata"]:
            brand_id = event_data["metadata"].get("brand_id")

        # For payment_intent events, check metadata
        if not brand_id and event_type.startswith("payment_intent"):
            brand_id = event_data.get("metadata", {}).get("brand_id")
            transaction_id = event_data.get("id")  # Use payment intent ID as transaction ID

        # For charge events, get from payment_intent
        if not brand_id and event_type.startswith("charge"):
            payment_intent_id = event_data.get("payment_intent")
            if payment_intent_id:
                try:
                    payment_intent = stripe.PaymentIntent.retrieve(payment_intent_id)
                    brand_id = payment_intent.get("metadata", {}).get("brand_id")
                    transaction_id = payment_intent_id  # Use payment intent ID as transaction ID
                except Exception as e:
                    logger.warning(f"Could not retrieve payment intent {payment_intent_id}: {e}")

        # For checkout session events
        if not brand_id and event_type.startswith("checkout.session"):
            brand_id = event_data.get("metadata", {}).get("brand_id")

        # Get payment intent from session to use as transaction ID for checkout events
        if event_type.startswith("checkout.session"):
            payment_intent_id = event_data.get("payment_intent")
            if payment_intent_id:
                transaction_id = payment_intent_id

        if not brand_id:
            logger.warning(f"No brand_id found for event {event_id} of type {event_type}")
            return

        if not transaction_id:
            logger.warning(f"No transaction_id found for event {event_id} of type {event_type}")
            return

        # Determine the subcollection based on event type
        if event_type.startswith("payment_intent"):
            subcollection_name = "paymentIntent"
        elif event_type.startswith("charge"):
            subcollection_name = "charges"
        elif event_type.startswith("checkout.session"):
            subcollection_name = "checkoutSession"
        else:
            subcollection_name = "other"

        # Store the event data
        event_doc_data = {
            "event_id": event_id,
            "event_type": event_type,
            "created": event["created"],
            "livemode": event["livemode"],
            "event_data": event_data,
            "full_event": event,  # Store complete event for reference
            "timestamp": firestore.SERVER_TIMESTAMP,
            "brand_id": brand_id,
            "transaction_id": transaction_id
        }

        # Store in wallets/{brand_id}/transactions/{transaction_id}/{subcollection_name}/{event_id}
        event_ref = (db.collection("wallets")
                    .document(brand_id)
                    .collection("transactions")
                    .document(transaction_id)
                    .collection(subcollection_name)
                    .document(event_id))

        event_ref.set(event_doc_data)

        logger.info(f"Stored {event_type} event {event_id} for brand {brand_id} in transaction {transaction_id}/{subcollection_name}")

    except Exception as e:
        logger.error(f"Error storing Stripe event: {str(e)}", exc_info=True)

# Helper function to handle one-time payment checkout
async def _handle_payment_checkout(session):
    """Handle successful one-time payment checkout - now just logs, actual processing happens in charge.updated"""
    metadata = session.get("metadata", {})

    try:
        brand_id = metadata["brand_id"]
        amount = metadata.get("amount", "unknown")
        payment_intent_id = session.get("payment_intent")

        logger.info(f"Checkout session completed for brand {brand_id}, amount: {amount}, payment_intent: {payment_intent_id}")
        logger.info("Wallet balance will be updated when charge.updated event is received")

    except (KeyError, ValueError, TypeError) as e:
        logger.error(f"Invalid metadata in checkout session: {e}")
        # Don't raise exception, just log - the charge.updated event will handle the actual processing
        raise HTTPException(status_code=500, detail="Failed to update wallet balance")

    logger.info(f"Transaction completed for brand_id {brand_id}, amount: {amount}")


# Helper function to handle successful charge
async def _handle_charge_succeeded(charge, event):
    """Handle successful charge and update wallet balance with Stripe fees accounted"""
    try:
        # Extract charge information
        charge_id = charge["id"]
        fee_before_processing_fee = charge["amount"]  # Amount in cents (what customer paid)
        payment_intent_id = charge.get("payment_intent")

        # Calculate Stripe processing fee: 3.1% + 30¢ (for tracking purposes)
        stripe_fee = int(fee_before_processing_fee * 0.031) + 30  # in cents
        fee_after_processing_fee = fee_before_processing_fee - stripe_fee  # Amount we actually receive

        # Get the payment intent to find the brand_id from metadata
        if not payment_intent_id:
            logger.error(f"No payment intent found for charge {charge_id}")
            return

        # Retrieve payment intent to get metadata
        payment_intent = stripe.PaymentIntent.retrieve(payment_intent_id)
        metadata = payment_intent.get("metadata", {})

        logger.info(f"Payment intent metadata: {metadata}")

        if not metadata.get("brand_id"):
            logger.error(f"No brand_id found in payment intent metadata for charge {charge_id}")
            logger.error(f"Available metadata keys: {list(metadata.keys())}")
            return

        brand_id = metadata["brand_id"]

        # Get the credit amount (what user wants added to wallet) from metadata
        credit_amount = int(metadata.get("amount", fee_before_processing_fee))  # Default to gross if not found

        logger.info(f"Processing successful charge {charge_id} for brand {brand_id}")
        logger.info(f"Fee before processing: ${fee_before_processing_fee/100:.2f}, Stripe fee: ${stripe_fee/100:.2f}, Fee after processing: ${fee_after_processing_fee/100:.2f}")
        logger.info(f"Credit amount (adding to wallet): ${credit_amount/100:.2f}")

        # Update wallet balance and create transaction record (add credit amount to wallet)
        _update_wallet_balance_from_charge(brand_id, credit_amount, fee_before_processing_fee, stripe_fee, charge_id, event)

        logger.info(f"Successfully processed charge {charge_id} for brand {brand_id}")

    except Exception as e:
        logger.exception(f"Error handling successful charge: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to process successful charge")


# Helper function to update wallet balance from charge
def _update_wallet_balance_from_charge(brand_id: str, credit_amount: int, fee_before_processing_fee: int, stripe_fee: int, charge_id: str, event: dict):
    """Update wallet balance and create transaction record for successful charge"""
    try:
        # Start a transaction to ensure atomic update
        @firestore.transactional
        def update_wallet_and_transactions(transaction):
            # Update wallet balance
            wallet_ref = db.collection("wallets").document(brand_id)
            wallet_doc = wallet_ref.get(transaction=transaction)

            new_balance_cents = 0  # Initialize the variable

            if wallet_doc.exists:
                wallet_data = wallet_doc.to_dict()
                # Get current balance in cents
                current_balance_cents = wallet_data.get("total_available_balance", 0)

                # Add credit amount (both in cents)
                new_balance_cents = current_balance_cents + credit_amount

                transaction.update(wallet_ref, {
                    "total_available_balance": new_balance_cents,
                    "updated_at": firestore.SERVER_TIMESTAMP
                })
            else:
                # Create wallet document if it doesn't exist
                new_balance_cents = credit_amount
                wallet_data = {
                    "brand_id": brand_id,
                    "total_available_balance": new_balance_cents,
                    "total_promo_available_balance": 0,
                    "total_promo_balance_spent": 0,
                    "total_balance_spent": 0,
                    "total_budget_allocated": 0,
                    "created_at": firestore.SERVER_TIMESTAMP,
                    "updated_at": firestore.SERVER_TIMESTAMP
                }
                transaction.set(wallet_ref, wallet_data)

            # Create main transaction record using payment intent ID as transaction ID
            payment_intent_id = event["data"]["object"].get("payment_intent")
            if payment_intent_id:
                tx_ref = wallet_ref.collection("transactions").document(payment_intent_id)
                fee_after_processing_fee = fee_before_processing_fee - stripe_fee
                tx_data = {
                    "type": "credit",
                    "category": "stripe_payment",
                    "fee_before_processing_fee": fee_before_processing_fee,  # What customer paid (in cents)
                    "stripe_fee": stripe_fee,      # Stripe processing fee (in cents)
                    "fee_after_processing_fee": fee_after_processing_fee,      # What we received (in cents)
                    "amount": credit_amount,          # Amount added to wallet (in cents)
                    "timestamp": firestore.SERVER_TIMESTAMP,
                    "description": f"Wallet deposit via Stripe",
                    "status": "completed",
                    "reference_id": charge_id,
                    "reference_type": "stripe_charge",
                    "payment_intent_id": payment_intent_id,
                    "transaction_id": payment_intent_id
                }
                transaction.set(tx_ref, tx_data)

            return new_balance_cents

        # Run the transaction
        transaction = db.transaction()
        new_balance = update_wallet_and_transactions(transaction)

        logger.info(f"Updated wallet balance for brand {brand_id}. New balance: ${new_balance/100:.2f} ({new_balance} cents)")

    except Exception as e:
        logger.exception(f"Error updating wallet balance from charge: {str(e)}")
        raise


class Transaction(BaseModel):
    id: str
    type: str
    amount: int
    timestamp: Dict[str, int]
    description: str
    status: str

@router.get("/wallet")
def get_wallet(user=Depends(require_role("brand"))) -> Dict[str, Any]:
    """Get wallet information including balance and transaction history."""
    brand_id = user["uid"]

    # Get wallet data from wallets collection
    wallet_ref = db.collection("wallets").document(brand_id)
    wallet_doc = wallet_ref.get()

    if not wallet_doc.exists:
        # If no wallet document exists yet, create a new one
        brand_ref = db.collection("brands").document(brand_id)
        brand_doc = brand_ref.get()

        if not brand_doc.exists:
            raise HTTPException(status_code=404, detail="Brand not found")

        # Create the wallet document with default values (all amounts in cents)
        wallet_data = {
            "brand_id": brand_id,
            "total_available_balance": 0,
            "total_promo_available_balance": 0,
            "total_promo_balance_spent": 0,
            "total_balance_spent": 0,
            "total_budget_allocated": 0,
            "created_at": firestore.SERVER_TIMESTAMP,
            "updated_at": firestore.SERVER_TIMESTAMP
        }
        wallet_ref.set(wallet_data)
        wallet_data = wallet_data  # Use the data we just created
    else:
        wallet_data = wallet_doc.to_dict()

    # Convert cents to dollars for API response
    total_available_balance_cents = wallet_data.get("total_available_balance", 0)
    total_promo_available_balance_cents = wallet_data.get("total_promo_available_balance", 0)
    total_promo_balance_spent_cents = wallet_data.get("total_promo_balance_spent", 0)
    total_balance_spent_cents = wallet_data.get("total_balance_spent", 0)
    total_budget_allocated_cents = wallet_data.get("total_budget_allocated", 0)

    return {
        "wallet_balance": total_available_balance_cents / 100,  # For backward compatibility (dollars)
        "total_spent": total_balance_spent_cents / 100,  # For backward compatibility (dollars)
        "current_total_budget": total_budget_allocated_cents / 100,  # For backward compatibility (dollars)
        "total_available_balance": total_available_balance_cents / 100,  # Dollars
        "total_promo_available_balance": total_promo_available_balance_cents / 100,  # Dollars
        "total_promo_balance_spent": total_promo_balance_spent_cents / 100,  # Dollars
        "total_balance_spent": total_balance_spent_cents / 100,  # Dollars
        "total_budget_allocated": total_budget_allocated_cents / 100  # Dollars
    }


@router.get("/transactions")
def get_transactions(user=Depends(require_role("brand"))) -> Dict[str, List[Dict[str, Any]]]:
    """Get transaction history for the authenticated brand."""
    brand_id = user["uid"]
    transactions: List[Dict[str, Any]] = []

    try:
        # Get transactions from the wallet transactions collection
        wallet_tx_ref = db.collection("wallets").document(brand_id).collection("transactions")
        wallet_tx_query = wallet_tx_ref.order_by("timestamp", direction=firestore.Query.DESCENDING)
        wallet_tx_docs = wallet_tx_query.stream()

        for doc in wallet_tx_docs:
            tx = doc.to_dict()
            tx["id"] = doc.id

            # Convert timestamp to a serializable format
            if "timestamp" in tx and tx["timestamp"] is not None:
                if hasattr(tx["timestamp"], "seconds"):
                    tx["timestamp"] = {
                        "seconds": tx["timestamp"].seconds,
                        "nanoseconds": tx["timestamp"].nanoseconds
                    }

            transactions.append(tx)

        return {"transactions": transactions}

    except Exception as e:
        logger.error(f"Error fetching transactions: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))