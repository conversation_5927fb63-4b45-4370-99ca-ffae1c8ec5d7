#!/usr/bin/env python3
"""
Test script for the budget allocation endpoint
"""
import requests
import json
import sys

def test_budget_allocation():
    """Test the budget allocation endpoint"""
    
    # Configuration
    API_BASE_URL = "http://localhost:8000"
    
    # Test data - you'll need to replace these with actual values
    OFFER_ID = "test_offer_id"  # Replace with actual offer ID
    BUDGET_AMOUNT = 5000  # 50 dollars in cents
    AUTH_TOKEN = "your_firebase_token_here"  # Replace with actual Firebase token
    
    print("🧪 Testing Budget Allocation Endpoint")
    print(f"API Base URL: {API_BASE_URL}")
    print(f"Offer ID: {OFFER_ID}")
    print(f"Budget Amount: ${BUDGET_AMOUNT/100:.2f}")
    print("-" * 50)
    
    # Test the budget allocation endpoint
    url = f"{API_BASE_URL}/offers/{OFFER_ID}/allocate-budget"
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {AUTH_TOKEN}"
    }
    payload = {
        "budget_amount": BUDGET_AMOUNT
    }
    
    try:
        print(f"📤 Making POST request to: {url}")
        print(f"📦 Payload: {json.dumps(payload, indent=2)}")
        
        response = requests.post(url, headers=headers, json=payload)
        
        print(f"📥 Response Status: {response.status_code}")
        print(f"📄 Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Success!")
            print(f"📊 Response: {json.dumps(result, indent=2)}")
        else:
            print("❌ Error!")
            try:
                error_data = response.json()
                print(f"🚨 Error Details: {json.dumps(error_data, indent=2)}")
            except:
                print(f"🚨 Error Text: {response.text}")
                
    except requests.exceptions.RequestException as e:
        print(f"🔥 Network Error: {e}")
    except Exception as e:
        print(f"🔥 Unexpected Error: {e}")

def test_endpoint_availability():
    """Test if the endpoint is available"""
    API_BASE_URL = "http://localhost:8000"
    
    try:
        # Test if the API is running
        response = requests.get(f"{API_BASE_URL}/docs")
        if response.status_code == 200:
            print("✅ API is running and accessible")
            return True
        else:
            print(f"⚠️ API returned status {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ API is not accessible: {e}")
        return False

if __name__ == "__main__":
    print("🚀 AdMesh Budget Allocation Test")
    print("=" * 50)
    
    # First check if API is available
    if test_endpoint_availability():
        print("\n")
        # Run the actual test
        test_budget_allocation()
    else:
        print("❌ Cannot proceed with tests - API is not available")
        sys.exit(1)
    
    print("\n" + "=" * 50)
    print("🏁 Test completed")
