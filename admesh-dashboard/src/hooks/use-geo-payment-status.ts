import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/hooks/use-auth';

interface GeoPaymentStatus {
  can_run_report: boolean;
  payment_required: boolean;
  total_reports_generated: number;
  remaining_free_reports: number;
  free_reports_limit: number;
  report_cost_dollars: number;
  wallet_balance_dollars: number;
  wallet_balance_cents: number;
  sufficient_balance: boolean;
  next_report_cost: {
    amount_dollars: number;
    amount_cents: number;
    is_free: boolean;
  };
}

interface UseGeoPaymentStatusReturn {
  paymentStatus: GeoPaymentStatus | null;
  loading: boolean;
  error: string | null;
  refreshStatus: () => Promise<void>;
}

export function useGeoPaymentStatus(): UseGeoPaymentStatusReturn {
  const { user, role } = useAuth();
  const [paymentStatus, setPaymentStatus] = useState<GeoPaymentStatus | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  const fetchPaymentStatus = useCallback(async () => {
    if (!user || role !== 'brand') {
      setPaymentStatus(null);
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const token = await user.getIdToken();
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/geo/payment-status`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch payment status');
      }

      const data = await response.json();
      setPaymentStatus(data);
    } catch (err) {
      console.error('Error fetching GEO payment status:', err);
      setError(err instanceof Error ? err.message : 'Unknown error occurred');
    } finally {
      setLoading(false);
    }
  }, [user, role]);

  // Initial fetch
  useEffect(() => {
    if (role === 'brand') {
      fetchPaymentStatus();
    }
  }, [fetchPaymentStatus, role]);

  return {
    paymentStatus,
    loading,
    error,
    refreshStatus: fetchPaymentStatus,
  };
}
