import React from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Wallet,
  Gift,
  AlertCircle,
  CreditCard,
  CheckCircle,
  DollarSign
} from "lucide-react";
import { useGeoPaymentStatus } from "@/hooks/use-geo-payment-status";
import { useWallet } from "@/hooks/use-wallet";
import { useRouter } from "next/navigation";

interface GEOPaymentStatusProps {
  onStatusChange?: (canRunReport: boolean) => void;
}

export function GEOPaymentStatus({ onStatusChange }: GEOPaymentStatusProps) {
  const { paymentStatus, loading, error } = useGeoPaymentStatus();
  const { walletBalance } = useWallet();
  const router = useRouter();

  // Notify parent component when status changes
  React.useEffect(() => {
    if (paymentStatus && onStatusChange) {
      onStatusChange(paymentStatus.can_run_report);
    }
  }, [paymentStatus, onStatusChange]);

  if (loading) {
    return (
      <Card className="border-gray-200 dark:border-gray-700">
        <CardContent className="p-6">
          <div className="flex items-center gap-3">
            <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600"></div>
            <span className="text-sm text-muted-foreground">Loading payment status...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error || !paymentStatus) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          Failed to load payment status. Please refresh the page.
        </AlertDescription>
      </Alert>
    );
  }

  const handleAddFunds = () => {
    router.push('/dashboard/brand/billing');
  };

  return (
    <Card className="border-gray-200 dark:border-gray-700">
      <CardHeader className="pb-4">
        <CardTitle className="flex items-center gap-2 text-lg">
          <Wallet className="h-5 w-5 text-blue-600" />
          GEO Report Status
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Free Reports Status */}
        <div className="flex items-center justify-between p-3 bg-green-50 dark:bg-green-950/20 rounded-lg border border-green-200 dark:border-green-800">
          <div className="flex items-center gap-3">
            <Gift className="h-5 w-5 text-green-600" />
            <div>
              <p className="font-medium text-green-800 dark:text-green-200">
                Free Reports
              </p>
              <p className="text-sm text-green-600 dark:text-green-400">
                {paymentStatus.remaining_free_reports} of {paymentStatus.free_reports_limit} remaining
              </p>
            </div>
          </div>
          <Badge variant={paymentStatus.remaining_free_reports > 0 ? "default" : "secondary"}>
            {paymentStatus.remaining_free_reports > 0 ? "Available" : "Used"}
          </Badge>
        </div>

        {/* Wallet Balance */}
        <div className="flex items-center justify-between p-3 bg-blue-50 dark:bg-blue-950/20 rounded-lg border border-blue-200 dark:border-blue-800">
          <div className="flex items-center gap-3">
            <DollarSign className="h-5 w-5 text-blue-600" />
            <div>
              <p className="font-medium text-blue-800 dark:text-blue-200">
                Wallet Balance
              </p>
              <p className="text-sm text-blue-600 dark:text-blue-400">
                ${paymentStatus.wallet_balance_dollars.toFixed(2)} available
              </p>
            </div>
          </div>
          <Badge variant={paymentStatus.sufficient_balance ? "default" : "destructive"}>
            {paymentStatus.sufficient_balance ? "Sufficient" : "Low"}
          </Badge>
        </div>

        {/* Next Report Cost */}
        <div className="p-3 bg-gray-50 dark:bg-gray-900 rounded-lg border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <p className="font-medium text-gray-800 dark:text-gray-200">
                Next Report Cost
              </p>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {paymentStatus.next_report_cost.is_free 
                  ? "Free" 
                  : `$${paymentStatus.next_report_cost.amount_dollars.toFixed(2)}`
                }
              </p>
            </div>
            {paymentStatus.can_run_report ? (
              <CheckCircle className="h-5 w-5 text-green-600" />
            ) : (
              <AlertCircle className="h-5 w-5 text-red-600" />
            )}
          </div>
        </div>

        {/* Action Required */}
        {!paymentStatus.can_run_report && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription className="flex flex-col gap-3">
              <p>
                You need ${paymentStatus.report_cost_dollars.toFixed(2)} in your wallet to run another GEO report.
              </p>
              <Button
                onClick={handleAddFunds}
                size="sm"
                className="w-fit"
              >
                <CreditCard className="h-4 w-4 mr-2" />
                Add Funds to Wallet
              </Button>
            </AlertDescription>
          </Alert>
        )}

        {/* Summary */}
        <div className="text-xs text-muted-foreground pt-2 border-t border-gray-200 dark:border-gray-700">
          <p>
            Total reports generated: {paymentStatus.total_reports_generated} • 
            Reports cost ${paymentStatus.report_cost_dollars.toFixed(2)} each after {paymentStatus.free_reports_limit} free reports
          </p>
        </div>
      </CardContent>
    </Card>
  );
}
