"use client";

import { useState, useEffect, useCallback } from "react";
import { useAuth } from "@/hooks/use-auth";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Search,
  AlertCircle,
  Target,
  BarChart3,
  RefreshCw,
  Download,
  History,
  ArrowLeft
} from "lucide-react";
import { GEOScoreCard } from "@/components/geo-check/GEOScoreCard";
import { GEORecommendationCard } from "@/components/geo-check/GEORecommendationCard";
import { GEOAnalysisLoader } from "@/components/geo-check/GEOAnalysisLoader";
import { GEOHistoryList } from "@/components/geo-check/GEOHistoryList";
import { GEOHistoricalReport } from "@/components/geo-check/GEOHistoricalReport";
import { GEOPaymentStatus } from "@/components/geo-check/GEOPaymentStatus";
import { GEOAnalysisOverview } from "@/components/geo-check/GEOAnalysisOverview";
import { GEOHistorySidebar } from "@/components/geo-check/GEOHistorySidebar";
import { useGeoPaymentStatus } from "@/hooks/use-geo-payment-status";

interface PageAnalysis {
  url: string;
  title: string;
  summary: string;
  score: number;
}

interface GEOAnalysis {
  overallScore: number;
  promptMentionRate: number;
  citationRate: number;
  websiteOptimization: number;
  sentimentTone: number;
  aiDiscoverability: {
    score: number;
    mentions: number;
    sentiment: "positive" | "neutral" | "negative";
    topQueries: string[];
  };
  contentOptimization: {
    score: number;
    structureScore: number;
    factualClaimsScore: number;
    aiReadabilityScore: number;
  };
  competitiveAnalysis: {
    shareOfVoice: number;
    competitorMentions: { name: string; mentions: number }[];
  };
  analyzedPages: PageAnalysis[];
  simulatedQueries: Array<{
    query: string;
    brand_mentioned: boolean;
    mention_context?: string;
    likelihood_score: number;
    reasoning: string;
  }>;
  recommendations: {
    priority: "high" | "medium" | "low";
    category: string;
    title: string;
    description: string;
    impact: string;
  }[];
  brandId: string;
}

interface GEOHistoryItem {
  id: string;
  created_at: string;
  overall_score: number;
  website_analyzed: string;
  brand_name: string;
  analyzed_pages_count: number;
  total_queries_simulated: number;
  prompt_mention_rate: number;
  citation_rate: number;
  website_optimization: number;
  sentiment_tone: number;
  analysis_version: string;
}

interface HistoricalGEOAnalysis {
  analysisId: string;
  overallScore: number;
  promptMentionRate: number;
  citationRate: number;
  websiteOptimization: number;
  sentimentTone: number;
  aiDiscoverability: {
    score: number;
    mentions: number;
    sentiment: "positive" | "neutral" | "negative";
    topQueries: string[];
  };
  contentOptimization: {
    score: number;
    structureScore: number;
    factualClaimsScore: number;
    aiReadabilityScore: number;
  };
  competitiveAnalysis: {
    shareOfVoice: number;
    competitorMentions: { name: string; mentions: number }[];
  };
  analyzedPages: Array<{
    url: string;
    title: string;
    summary: string;
    score: number;
  }>;
  simulatedQueries: Array<{
    query: string;
    brand_mentioned: boolean;
    mention_context?: string;
    likelihood_score: number;
    reasoning: string;
  }>;
  recommendations: Array<{
    priority: "high" | "medium" | "low";
    category: string;
    title: string;
    description: string;
    impact: string;
  }>;
  analyzedAt: string;
  brandId: string;
  websiteAnalyzed: string;
  brandName: string;
  isHistorical: boolean;
  analysisVersion: string;
}

export default function GEOCheckPage() {
  const { user } = useAuth();
  const [analysis, setAnalysis] = useState<GEOAnalysis | null>(null);
  const [loading, setLoading] = useState(false);
  const [brandData, setBrandData] = useState<{
    website?: string;
    company_name?: string;
    industry?: string;
  } | null>(null);

  // History state management
  const [currentView, setCurrentView] = useState<'analysis' | 'history' | 'historical-report'>('analysis');
  const [history, setHistory] = useState<GEOHistoryItem[]>([]);
  const [historyLoading, setHistoryLoading] = useState(false);
  const [selectedHistoricalAnalysis, setSelectedHistoricalAnalysis] = useState<HistoricalGEOAnalysis | null>(null);
  const [historyPagination, setHistoryPagination] = useState({
    offset: 0,
    limit: 10,
    hasMore: false,
    total: 0
  });

  // Payment status state
  const [canRunReport, setCanRunReport] = useState(false);
  const { paymentStatus, refreshStatus } = useGeoPaymentStatus();

  const fetchBrandData = useCallback(async () => {
    if (!user) return;

    try {
      const token = await user.getIdToken();
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/brands/profile`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setBrandData(data);
      }
    } catch (error) {
      console.error("Error fetching brand data:", error);
    }
  }, [user]);

  useEffect(() => {
    fetchBrandData();
  }, [fetchBrandData]);

  // Fetch GEO analysis history
  const fetchHistory = useCallback(async (loadMore = false) => {
    if (!user) return;

    setHistoryLoading(true);
    try {
      const token = await user.getIdToken();
      const offset = loadMore ? historyPagination.offset + historyPagination.limit : 0;

      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/geo/history?limit=${historyPagination.limit}&offset=${offset}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.ok) {
        const data = await response.json();

        if (loadMore) {
          setHistory(prev => [...prev, ...data.history]);
        } else {
          setHistory(data.history);
        }

        setHistoryPagination({
          offset: offset,
          limit: historyPagination.limit,
          hasMore: data.pagination.has_more,
          total: data.pagination.total
        });
      } else {
        console.error("Failed to fetch history");
      }
    } catch (error) {
      console.error("Error fetching history:", error);
    } finally {
      setHistoryLoading(false);
    }
  }, [user, historyPagination.limit, historyPagination.offset]);

  // Fetch specific historical analysis
  const fetchHistoricalAnalysis = useCallback(async (analysisId: string) => {
    if (!user) return;

    setLoading(true);
    try {
      const token = await user.getIdToken();
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/geo/history/${analysisId}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.ok) {
        const data = await response.json();
        setSelectedHistoricalAnalysis(data);
        setCurrentView('historical-report');
      } else {
        const errorData = await response.json().catch(() => ({}));
        alert(`Failed to load historical analysis: ${errorData.detail || 'Please try again.'}`);
      }
    } catch (error) {
      console.error("Error fetching historical analysis:", error);
      alert("Failed to load historical analysis. Please try again.");
    } finally {
      setLoading(false);
    }
  }, [user]);

  // Load history when switching to history view
  useEffect(() => {
    if (currentView === 'history' && history.length === 0) {
      fetchHistory();
    }
  }, [currentView, history.length, fetchHistory]);

  const runGEOReport = async () => {
    if (!brandData?.website) {
      alert("Please add your website in the brand profile first.");
      return;
    }

    // Check if user can run report before proceeding
    if (!canRunReport) {
      alert("Please check your payment status. You may need to add funds to your wallet.");
      return;
    }

    setLoading(true);
    try {
      const token = await user?.getIdToken();
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/geo/check`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          brand_id: null // Will use authenticated user's brand
        }),
      });

      if (response.ok) {
        const data = await response.json();
        setAnalysis(data);
        setCurrentView('analysis');

        // Refresh payment status after successful report
        refreshStatus();

        // Refresh history to include the new analysis
        if (history.length > 0) {
          fetchHistory();
        }
      } else {
        const errorData = await response.json().catch(() => ({}));

        // Handle payment-specific errors
        if (response.status === 402) {
          const paymentError = errorData.detail;
          if (typeof paymentError === 'object' && paymentError.error) {
            alert(`Payment Required: ${paymentError.message}\n\nWallet Balance: $${paymentError.wallet_balance}\nRequired: $${paymentError.required_amount}`);
          } else {
            alert("Payment required. Please add funds to your wallet.");
          }
          // Refresh payment status to update UI
          refreshStatus();
        } else {
          throw new Error(errorData.detail || "Failed to run GEO analysis");
        }
      }
    } catch (error) {
      console.error("Error running GEO analysis:", error);
      alert(`Failed to run GEO analysis: ${error instanceof Error ? error.message : 'Please try again.'}`);
    } finally {
      setLoading(false);
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 80) return "text-green-600";
    if (score >= 60) return "text-yellow-600";
    return "text-red-600";
  };

  const getScoreBadgeVariant = (score: number) => {
    if (score >= 80) return "default";
    if (score >= 60) return "secondary";
    return "destructive";
  };

  const exportToPDF = () => {
    if (!analysis) return;

    // Create a simple HTML report for PDF generation
    const reportContent = `
      <html>
        <head>
          <title>GEO Analysis Report - ${brandData?.company_name || 'Brand'}</title>
          <style>
            body {
              font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
              margin: 20px;
              line-height: 1.6;
              color: #333;
            }
            .header {
              text-align: center;
              margin-bottom: 40px;
              padding-bottom: 20px;
              border-bottom: 2px solid #e5e7eb;
            }
            .score {
              font-size: 28px;
              font-weight: bold;
              color: #2563eb;
              margin-top: 15px;
            }
            .section {
              margin-bottom: 30px;
              page-break-inside: avoid;
            }
            .section h3 {
              color: #1f2937;
              border-bottom: 2px solid #e5e7eb;
              padding-bottom: 8px;
              margin-bottom: 15px;
            }
            .metric {
              display: inline-block;
              margin: 8px;
              padding: 12px;
              border: 1px solid #d1d5db;
              border-radius: 8px;
              background: #f9fafb;
              min-width: 200px;
            }
            .recommendation {
              margin: 15px 0;
              padding: 15px;
              border-left: 4px solid #2563eb;
              background: #f8fafc;
              border-radius: 6px;
              box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            }
            .page {
              margin: 12px 0;
              padding: 12px;
              border: 1px solid #e5e7eb;
              border-radius: 6px;
              background: #fafafa;
            }
            @media print {
              .section { page-break-inside: avoid; }
              .recommendation { page-break-inside: avoid; }
            }
          </style>
        </head>
        <body>
          <div class="header">
            <h1>GEO Report</h1>
            <h2>${brandData?.company_name || 'Brand'}</h2>
            <p>Generated on ${new Date().toLocaleDateString()}</p>
            <div class="score">Overall GEO Score: ${analysis.overallScore}/100</div>
          </div>

          <div class="section">
            <h3>Score Breakdown</h3>
            <div class="metric">Prompt Mention Rate: ${Math.round(analysis.promptMentionRate)}% (40% weight)</div>
            <div class="metric">Citation Rate: ${Math.round(analysis.citationRate)}% (20% weight)</div>
            <div class="metric">Website Optimization: ${analysis.websiteOptimization}/100 (30% weight)</div>
            <div class="metric">Sentiment/Tone: ${analysis.sentimentTone}/100 (10% weight)</div>
          </div>

          <div class="section">
            <h3>Analyzed Pages (${analysis.analyzedPages.length})</h3>
            ${analysis.analyzedPages.map(page => `
              <div class="page">
                <strong>${page.title}</strong> (Score: ${page.score}/100)<br>
                <small>${page.url}</small><br>
                ${page.summary}
              </div>
            `).join('')}
          </div>

          <div class="section">
            <h3>Actionable GEO Recommendations</h3>
            <p style="margin-bottom: 15px; color: #666; font-style: italic;">
              These AI-powered recommendations are specifically tailored to improve your brand's visibility in AI-generated responses.
            </p>
            ${analysis.recommendations.map((rec, index) => `
              <div class="recommendation" style="margin-bottom: 20px; page-break-inside: avoid;">
                <div style="display: flex; align-items: center; margin-bottom: 8px;">
                  <span style="background: ${rec.priority === 'high' ? '#dc2626' : rec.priority === 'medium' ? '#d97706' : '#16a34a'};
                               color: white; padding: 2px 8px; border-radius: 12px; font-size: 12px; font-weight: bold; margin-right: 10px;">
                    ${rec.priority.toUpperCase()}
                  </span>
                  <span style="background: #f3f4f6; padding: 2px 8px; border-radius: 12px; font-size: 12px; color: #374151;">
                    ${rec.category}
                  </span>
                </div>
                <h4 style="margin: 8px 0; font-size: 16px; color: #1f2937;">${index + 1}. ${rec.title}</h4>
                <p style="margin: 8px 0; line-height: 1.5; color: #4b5563;">${rec.description}</p>
                <div style="background: #f0f9ff; padding: 10px; border-radius: 6px; border-left: 4px solid #0ea5e9; margin-top: 10px;">
                  <strong style="color: #0c4a6e;">Expected Impact:</strong> ${rec.impact}
                </div>
              </div>
            `).join('')}
          </div>

          <div class="section">
            <h3>AI Query Simulation</h3>
            ${analysis.simulatedQueries.map(query => `
              <div class="page">
                <strong>"${query.query}"</strong><br>
                Brand Mentioned: ${query.brand_mentioned ? 'Yes' : 'No'}<br>
                ${query.mention_context ? `Context: ${query.mention_context}<br>` : ''}
                Likelihood Score: ${query.likelihood_score}/100<br>
                ${query.reasoning}
              </div>
            `).join('')}
          </div>
        </body>
      </html>
    `;

    // Create a new window and print
    const printWindow = window.open('', '_blank');
    if (printWindow) {
      printWindow.document.write(reportContent);
      printWindow.document.close();
      printWindow.focus();
      setTimeout(() => {
        printWindow.print();
        printWindow.close();
      }, 250);
    }
  };



  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-50/20 dark:from-gray-900 dark:via-blue-950/20 dark:to-indigo-950/10">
      <div className="container mx-auto px-4 py-6 max-w-7xl">
        {/* Premium Header Section */}
        <div className="bg-gradient-to-r from-white to-blue-50/50 dark:from-gray-800 dark:to-blue-950/30 rounded-2xl shadow-lg border border-slate-200/60 dark:border-gray-700/60 p-8 mb-6">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
            <div className="flex items-start gap-6">
              {currentView === 'historical-report' && (
                <Button
                  onClick={() => setCurrentView('analysis')}
                  variant="outline"
                  size="sm"
                  className={`gap-2 shrink-0 ${loading ? 'opacity-50 cursor-not-allowed' : ''}`}
                  disabled={loading}
                  title={loading ? 'Navigation disabled during analysis' : ''}
                >
                  <ArrowLeft className="h-4 w-4" />
                  Back to Dashboard
                </Button>
              )}

              <div className="space-y-3">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-lg">
                    <BarChart3 className="h-6 w-6 text-white" />
                  </div>
                  <h1 className="text-3xl font-bold tracking-tight bg-gradient-to-r from-slate-900 to-blue-800 dark:from-white dark:to-blue-200 bg-clip-text text-transparent">
                    {currentView === 'historical-report' ? 'Historical GEO Analysis' : 'AI Visibility Intelligence'}
                  </h1>
                </div>
                <h2 className="text-xl text-slate-700 dark:text-gray-200 font-semibold">
                  Comprehensive AI Platform Analysis • 4 Platforms • 200 Queries • 5 Countries
                </h2>
                <p className="text-slate-600 dark:text-gray-300 leading-relaxed max-w-4xl">
                  {currentView === 'historical-report' ? 'Detailed historical analysis with comprehensive insights and performance tracking' :
                   'Professional-grade analysis across ChatGPT, Perplexity, Gemini, and Grok. Get actionable insights to optimize your brand visibility in AI-generated responses with data from 200 targeted queries across global markets.'}
                </p>
              </div>
            </div>

            <div className="flex flex-wrap gap-4">
              {/* Export button for current analysis */}
              {analysis && currentView === 'analysis' && (
                <Button
                  onClick={exportToPDF}
                  variant="outline"
                  className="gap-2 min-w-[140px] border-slate-300 dark:border-gray-600 hover:bg-slate-50 dark:hover:bg-gray-700"
                >
                  <Download className="h-4 w-4" />
                  Export PDF Report
                </Button>
              )}

              {/* Historical report actions */}
              {currentView === 'historical-report' && selectedHistoricalAnalysis && (
                <>
                  <Button
                    onClick={() => {
                      // Export historical report to PDF
                      const reportContent = `
                        <html>
                          <head>
                            <title>Historical GEO Analysis Report - ${selectedHistoricalAnalysis.brandName}</title>
                            <style>
                              body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; line-height: 1.6; color: #333; }
                              .header { text-align: center; margin-bottom: 40px; padding-bottom: 20px; border-bottom: 2px solid #e5e7eb; }
                              .score { font-size: 28px; font-weight: bold; color: #2563eb; margin-top: 15px; }
                            </style>
                          </head>
                          <body>
                            <div class="header">
                              <h1>Historical GEO Analysis Report</h1>
                              <h2>${selectedHistoricalAnalysis.brandName}</h2>
                              <p>Analysis Date: ${new Date(selectedHistoricalAnalysis.analyzedAt).toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric', hour: '2-digit', minute: '2-digit' })}</p>
                              <p>Website: ${selectedHistoricalAnalysis.websiteAnalyzed}</p>
                              <div class="score">Overall GEO Score: ${selectedHistoricalAnalysis.overallScore}/100</div>
                            </div>
                          </body>
                        </html>
                      `;
                      const printWindow = window.open('', '_blank');
                      if (printWindow) {
                        printWindow.document.write(reportContent);
                        printWindow.document.close();
                        printWindow.focus();
                        setTimeout(() => {
                          printWindow.print();
                          printWindow.close();
                        }, 250);
                      }
                    }}
                    variant="outline"
                    className="gap-2 min-w-[120px]"
                  >
                    <Download className="h-4 w-4" />
                    Export PDF
                  </Button>
                  <Button
                    onClick={runGEOReport}
                    disabled={loading || !brandData?.website}
                    className="gap-2 min-w-[140px] bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800"
                  >
                    {loading ? (
                      <RefreshCw className="h-4 w-4 animate-spin" />
                    ) : (
                      <Search className="h-4 w-4" />
                    )}
                    {loading ? "Analyzing..." : "Run New Analysis"}
                  </Button>
                </>
              )}

              {/* Premium Run Analysis Button */}
              {currentView !== 'historical-report' && (
                <Button
                  onClick={runGEOReport}
                  disabled={loading || !brandData?.website || !canRunReport}
                  size="lg"
                  className="gap-3 min-w-[200px] bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 hover:from-blue-700 hover:via-indigo-700 hover:to-purple-700 text-white font-semibold shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105"
                >
                  {loading ? (
                    <>
                      <RefreshCw className="h-5 w-5 animate-spin" />
                      <span>Analyzing 200 Queries...</span>
                    </>
                  ) : (
                    <>
                      <Search className="h-5 w-5" />
                      <span>
                        {paymentStatus?.next_report_cost.is_free
                          ? "Start Free Analysis"
                          : `Run Premium Analysis ($${paymentStatus?.next_report_cost.amount_dollars.toFixed(2) || '9.00'})`
                        }
                      </span>
                    </>
                  )}
                </Button>
              )}
            </div>
          </div>
        </div>

        {/* Main Dashboard Layout */}
        <div className="grid grid-cols-1 xl:grid-cols-4 gap-6">
          {/* Main Content Area */}
          <div className="xl:col-span-3 space-y-6">
            {/* Payment Status Section */}
            {currentView !== 'historical-report' && (
              <GEOPaymentStatus
                onStatusChange={setCanRunReport}
              />
            )}

            {/* Historical Report View */}
            {currentView === 'historical-report' && selectedHistoricalAnalysis && (
              <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg border border-slate-200/60 dark:border-gray-700/60">
                <GEOHistoricalReport
                  analysis={selectedHistoricalAnalysis}
                />
              </div>
            )}

            {/* Analysis Overview or Loading */}
            {currentView === 'analysis' && (
              <>
                {loading ? (
                  <GEOAnalysisLoader
                    isLoading={loading}
                    brandName={brandData?.company_name}
                    website={brandData?.website}
                  />
                ) : analysis ? (
                  <div className="space-y-6">
                    {/* Analysis Results */}
                    <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg border border-slate-200/60 dark:border-gray-700/60 p-8">
                      <div className="mb-8">
                        <h2 className="text-2xl font-bold text-slate-900 dark:text-white mb-2">
                          Analysis Results
                        </h2>
                        <p className="text-slate-600 dark:text-gray-300">
                          Comprehensive AI visibility analysis across 4 platforms and 200 queries
                        </p>
                      </div>

                      {/* Score Overview */}
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                        <GEOScoreCard
                          title="Overall Score"
                          score={analysis.overallScore}
                          description="Combined performance metric"
                        />
                        <GEOScoreCard
                          title="Mention Rate"
                          score={Math.round(analysis.promptMentionRate)}
                          description="40% weight - Brand appears in responses"
                        />
                        <GEOScoreCard
                          title="Citation Rate"
                          score={Math.round(analysis.citationRate)}
                          description="20% weight - Website links in outputs"
                        />
                        <GEOScoreCard
                          title="Content Score"
                          score={analysis.websiteOptimization}
                          description="30% weight - Content quality"
                        />
                      </div>
                    </div>
                  </div>
                ) : (
                  <GEOAnalysisOverview hasAnalysis={false} isLoading={false} />
                )}
              </>
            )}
          </div>

          {/* Sidebar */}
          <div className="xl:col-span-1 space-y-6">
            <GEOHistorySidebar
              history={history}
              loading={historyLoading}
              onViewReport={fetchHistoricalAnalysis}
              onViewFullHistory={() => setCurrentView('history')}
              currentAnalysis={analysis}
            />
          </div>
        </div>

        {/* Full History View */}
        {currentView === 'history' && (
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg border border-slate-200/60 dark:border-gray-700/60">
            <GEOHistoryList
              history={history}
              loading={historyLoading}
              onViewReport={fetchHistoricalAnalysis}
              onLoadMore={() => fetchHistory(true)}
              hasMore={historyPagination.hasMore}
              currentAnalysis={analysis}
            />
          </div>
        )}
      </div>
    </div>
  );
}
