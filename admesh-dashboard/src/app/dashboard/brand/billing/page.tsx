"use client";

import { useEffect, useState, useCallback } from "react";
import { useAuth } from "@/hooks/use-auth";
import { centsToDollars, formatCurrency as formatCurrencyUtil } from "@/lib/utils";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  CreditCard,
  CircleDollarSign,
  DollarSign,
  Shield,
  ArrowDownToLine,
} from "lucide-react";
import { toast } from "sonner";
import DashboardFooter from "@/components/DashboardFooter";

export default function BillingPage() {
  const { user } = useAuth();
  const [walletData, setWalletData] = useState({
    total_available_balance: 0,
    total_promo_available_balance: 0,
    total_promo_balance_spent: 0,
    total_balance_spent: 0,
    total_budget_allocated: 0
  });
  const [transactions, setTransactions] = useState<
    { id: string; type: string; amount: number; timestamp: Date | null; offer_title?: string; description?: string; reference_id?: string; reference_type?: string; category?: string; balance_after?: number; source?: string }[]
  >([]);
  const [showAddFunds, setShowAddFunds] = useState(false);
  const [addAmount, setAddAmount] = useState("");
  const [isRedirecting, setIsRedirecting] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Check for success parameter in URL (from Stripe redirect)
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const success = urlParams.get('success');
    const canceled = urlParams.get('canceled');

    if (success === 'true') {
      alert('Payment successful! Your wallet has been updated.');
      // Remove the query parameters from the URL
      window.history.replaceState({}, document.title, window.location.pathname);
    } else if (canceled === 'true') {
      alert('Payment canceled.');
      // Remove the query parameters from the URL
      window.history.replaceState({}, document.title, window.location.pathname);
    }
  }, []);

  // Function to fetch billing data
  const fetchBillingData = useCallback(async () => {
    if (!user) return;

    setIsLoading(true);
    setError(null);

    try {
      const token = await user.getIdToken();

      const [walletRes, txRes] = await Promise.all([
        fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/billing/wallet`, {
          headers: { Authorization: `Bearer ${token}` },
        }),
        fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/billing/transactions`, {
          headers: { Authorization: `Bearer ${token}` },
        }),
      ]);

      if (!walletRes.ok) {
        throw new Error(`Failed to fetch wallet data: ${walletRes.status}`);
      }

      if (!txRes.ok) {
        throw new Error(`Failed to fetch transaction data: ${txRes.status}`);
      }

      const walletData = await walletRes.json();
      const txData = await txRes.json();

      // Define types for different timestamp formats
      type FirestoreTimestamp = { seconds: number; nanoseconds?: number };
      type AlternativeTimestamp = { _seconds: number };
      type TimestampFormat = string | FirestoreTimestamp | AlternativeTimestamp;

      // Type guard functions
      const isFirestoreTimestamp = (timestamp: unknown): timestamp is FirestoreTimestamp =>
        typeof timestamp === 'object' && timestamp !== null && 'seconds' in timestamp;

      const isAlternativeTimestamp = (timestamp: unknown): timestamp is AlternativeTimestamp =>
        typeof timestamp === 'object' && timestamp !== null && '_seconds' in timestamp;

      const normalizedTx = txData.transactions?.map((tx: { id: string; type: string; amount: number; timestamp: TimestampFormat; offer_title?: string; description?: string; category?: string; reference_id?: string; reference_type?: string; balance_after?: number; source?: string }) => {
        // Handle different timestamp formats
        let parsedTimestamp = null;

        if (tx.timestamp) {
          if (typeof tx.timestamp === "string") {
            // ISO string format
            parsedTimestamp = new Date(tx.timestamp);
          } else if (typeof tx.timestamp === "object") {
            if (isFirestoreTimestamp(tx.timestamp)) {
              // Firestore timestamp format with seconds
              const seconds = typeof tx.timestamp.seconds === "number"
                ? tx.timestamp.seconds
                : parseInt(tx.timestamp.seconds);

              const nanoseconds = tx.timestamp.nanoseconds
                ? (typeof tx.timestamp.nanoseconds === "number"
                  ? tx.timestamp.nanoseconds
                  : parseInt(tx.timestamp.nanoseconds)) / 1000000
                : 0;

              parsedTimestamp = new Date(seconds * 1000 + nanoseconds);
            } else if (isAlternativeTimestamp(tx.timestamp)) {
              // Alternative Firestore format
              parsedTimestamp = new Date(tx.timestamp._seconds * 1000);
            }
          }
        }

        return {
          ...tx,
          timestamp: parsedTimestamp
        };
      });

      // Set all the data from the wallet collection
      setWalletData({
        total_available_balance: walletData.total_available_balance ?? 0,
        total_promo_available_balance: walletData.total_promo_available_balance ?? 0,
        total_promo_balance_spent: walletData.total_promo_balance_spent ?? 0,
        total_balance_spent: walletData.total_balance_spent ?? 0,
        total_budget_allocated: walletData.total_budget_allocated ?? 0
      });
      setTransactions(normalizedTx ?? []);
    } catch (err) {
      console.error("🔥 Error fetching billing data:", err);
      setError(err instanceof Error ? err.message : 'Failed to load billing data');
    } finally {
      setIsLoading(false);
    }
  }, [user]);

  // Fetch billing data when user changes or after successful payment
  useEffect(() => {
    if (!user) return;
    fetchBillingData();
  }, [user, fetchBillingData]);

  const handleStripeCheckout = async () => {
    if (!user || !addAmount) {
      alert('Please enter a valid amount');
      return;
    }

    // Parse the amount and validate it
    const amount = parseFloat(addAmount);
    if (isNaN(amount) || amount <= 0) {
      alert('Please enter a valid amount');
      return;
    }

    // Make sure the amount has at most 2 decimal places
    const roundedAmount = Math.round(amount * 100) / 100;
    if (roundedAmount !== amount) {
      setAddAmount(roundedAmount.toString());
    }

    setIsRedirecting(true);

    try {
      // Use the rounded amount to avoid floating point precision issues
      const finalAmount = roundedAmount;

      // Convert dollar amount to cents for the API (ensure integers)
      const creditAmountCents = Math.round(finalAmount * 100);
      const stripeFee = Math.round((finalAmount * 0.031 + 0.30) * 100);
      const totalAmountCents = creditAmountCents + stripeFee;

      const token = await user.getIdToken();

      // Log the values we're sending to the API for debugging
      console.log('Sending to API:', {
        amount: totalAmountCents,
        credit_amount: creditAmountCents,
        amount_type: typeof totalAmountCents,
        credit_amount_type: typeof creditAmountCents,
        amount_is_integer: Number.isInteger(totalAmountCents),
        credit_amount_is_integer: Number.isInteger(creditAmountCents)
      });

      const res = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/billing/create-checkout-session`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          // Send amounts in cents to the API
          amount: totalAmountCents,
          credit_amount: creditAmountCents,
        }),
      });

      if (!res.ok) {
        // Try to parse the error response
        try {
          const errorData = await res.json();
          console.error('API Error:', errorData);
          throw new Error(`Failed to create checkout session: ${errorData.detail || res.status}`);
        } catch {
          // If we can't parse the error response, just use the status code
          throw new Error(`Failed to create checkout session: ${res.status}`);
        }
      }

      const data = await res.json();

      if (data.session_url) {
        toast.success("Redirecting to secure checkout...");
        // Small delay to show the toast before redirect
        setTimeout(() => {
          window.location.href = data.session_url;
        }, 500);
      } else {
        throw new Error("Stripe session URL missing");
      }
    } catch (err) {
      console.error("❌ Stripe checkout error:", err);
      toast.error(err instanceof Error ? err.message : 'Failed to create checkout session');
      setIsRedirecting(false);
    }
  };

  // Format currency values
  // API now returns wallet values in dollars, so we can format them directly
  const formatCurrency = (value: number) => {
    return formatCurrencyUtil(value);
  };

  // Format transaction amounts (stored in cents in database)
  const formatTransactionAmount = (value: number) => {
    const dollars = centsToDollars(value);
    return formatCurrencyUtil(dollars);
  };

  return (
    <div className="p-8 space-y-8 max-w-7xl mx-auto">
      {/* Header Section */}
      <div className="space-y-4">
        <div className="flex justify-between items-start">
          <div className="space-y-1">
            <h1 className="text-3xl font-bold tracking-tight text-gray-900">Billing & Wallet</h1>
            <p className="text-lg text-muted-foreground">
              Manage your wallet balance and view transaction history
            </p>
          </div>
          <div className="flex items-center gap-3">
            <Button
              variant="ghost"
              size="icon"
              onClick={fetchBillingData}
              disabled={isLoading}
              className="rounded-full h-10 w-10"
              title="Refresh"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="18"
                height="18"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className={isLoading ? "animate-spin" : ""}
              >
                <path d="M21 12a9 9 0 0 0-9-9 9.75 9.75 0 0 0-6.74 2.74L3 8" />
                <path d="M3 3v5h5" />
                <path d="M3 12a9 9 0 0 0 9 9 9.75 9.75 0 0 0 6.74-2.74L21 16" />
                <path d="M16 21h5v-5" />
              </svg>
            </Button>
            <Button onClick={() => setShowAddFunds(true)} size="lg">
              Add Funds
            </Button>
          </div>
        </div>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
          <p>{error}</p>
          <Button
            variant="link"
            className="text-red-700 p-0 h-auto text-sm"
            onClick={fetchBillingData}
          >
            Try again
          </Button>
        </div>
      )}

      {/* Wallet Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="border-0 shadow-lg bg-white">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="text-sm font-medium text-muted-foreground uppercase tracking-wide">
                Available Balance
              </CardTitle>
              <div className="p-2 bg-green-50 rounded-lg">
                <CreditCard className="w-5 h-5 text-green-600" />
              </div>
            </div>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="text-3xl font-bold text-gray-900">
              {/* API returns balance in dollars, format for display */}
              {formatCurrency(walletData.total_available_balance)}
            </div>
            <p className="text-sm text-muted-foreground mt-1">
              Ready to spend
            </p>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg bg-white">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="text-sm font-medium text-muted-foreground uppercase tracking-wide">
                Total Spent
              </CardTitle>
              <div className="p-2 bg-red-50 rounded-lg">
                <CircleDollarSign className="w-5 h-5 text-red-600" />
              </div>
            </div>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="text-3xl font-bold text-gray-900">
              {/* API returns total spent in dollars, format for display */}
              {formatCurrency(walletData.total_balance_spent)}
            </div>
            <p className="text-sm text-muted-foreground mt-1">
              Campaign spending
            </p>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg bg-white">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="text-sm font-medium text-muted-foreground uppercase tracking-wide">
                Budget Allocated
              </CardTitle>
              <div className="p-2 bg-blue-50 rounded-lg">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-blue-600">
                  <rect width="20" height="14" x="2" y="5" rx="2" />
                  <line x1="2" x2="22" y1="10" y2="10" />
                </svg>
              </div>
            </div>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="text-3xl font-bold text-gray-900">
              {/* API returns budget allocated in dollars, format for display */}
              {formatCurrency(walletData.total_budget_allocated)}
            </div>
            <p className="text-sm text-muted-foreground mt-1">
              Active campaigns
            </p>
          </CardContent>
        </Card>
      </div>



      {/* Transaction History */}
      <Card className="border-0 shadow-lg bg-white">
        <CardHeader className="pb-6">
          <CardTitle className="text-xl font-semibold text-gray-900">Transaction History</CardTitle>
          <p className="text-sm text-muted-foreground mt-1">
            View all your wallet transactions and spending activity
          </p>
        </CardHeader>
        <CardContent className="p-0">
          {isLoading ? (
            <div className="p-6 text-center">
              <div className="inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-current border-r-transparent align-[-0.125em] motion-reduce:animate-[spin_1.5s_linear_infinite]" role="status">
                <span className="!absolute !-m-px !h-px !w-px !overflow-hidden !whitespace-nowrap !border-0 !p-0 ![clip:rect(0,0,0,0)]">Loading...</span>
              </div>
              <p className="mt-2 text-sm text-muted-foreground">Loading transactions...</p>
            </div>
          ) : transactions.length === 0 ? (
            <div className="p-12 text-center">
              <div className="mx-auto w-20 h-20 bg-gray-50 rounded-full flex items-center justify-center mb-6">
                <CreditCard className="w-10 h-10 text-gray-400" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">No transactions yet</h3>
              <p className="text-muted-foreground mb-6 max-w-sm mx-auto">
                Add funds to your wallet to start powering your campaigns and see your transaction history here
              </p>
              <Button
                onClick={() => setShowAddFunds(true)}
                size="lg"
              >
                Add Your First Funds
              </Button>
            </div>
          ) : (
            <ul className="divide-y divide-gray-100">
              {transactions.map((tx) => (
                <li key={tx.id} className="p-6 hover:bg-gray-50 transition-colors">
                  <div className="flex justify-between items-center">
                    <span className="flex items-center">
                      {/* Legacy transaction types */}
                      {tx.type === "add_funds" && (
                        <>
                          <span className="inline-flex items-center justify-center w-8 h-8 rounded-full bg-green-100 text-green-800 mr-3">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M12 2v20M2 12h20"/></svg>
                          </span>
                          <span className="font-medium">Added funds</span>
                        </>
                      )}
                      {tx.type === "recharge_offer" && (
                        <>
                          <span className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-blue-100 text-blue-800 mr-2">
                            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M21.5 2v6h-6M2.5 22v-6h6M2 11.5a10 10 0 0 1 18.8-4.3M22 12.5a10 10 0 0 1-18.8 4.2"/></svg>
                          </span>
                          <span>Recharged &quot;{tx.offer_title || 'Offer'}&quot;</span>
                        </>
                      )}
                      {tx.type === "spend" && (
                        <>
                          <span className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-red-100 text-red-800 mr-2">
                            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M12 2v20M2 12h20"/></svg>
                          </span>
                          <span>Spent on campaign</span>
                        </>
                      )}

                      {/* New transaction types */}
                      {tx.type === "credit" && tx.category === "stripe_payment" && (
                        <>
                          <span className="inline-flex items-center justify-center w-8 h-8 rounded-full bg-green-100 text-green-800 mr-3">
                            <ArrowDownToLine className="w-4 h-4" />
                          </span>
                          <span className="font-medium">{tx.description || "Wallet deposit"}</span>
                        </>
                      )}
                      {tx.type === "credit" && tx.category === "promo_credit" && (
                        <>
                          <span className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-yellow-100 text-yellow-800 mr-2">
                            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"/></svg>
                          </span>
                          <span>{tx.description || "Promo credit added"}</span>
                        </>
                      )}
                      {tx.type === "debit" && tx.category === "budget_allocation" && (
                        <>
                          <span className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-blue-100 text-blue-800 mr-2">
                            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><rect width="20" height="14" x="2" y="5" rx="2" /><line x1="2" x2="22" y1="10" y2="10" /></svg>
                          </span>
                          <span>
                            {tx.description || "Budget allocated"}
                            {tx.offer_title && <> for &quot;{tx.offer_title}&quot;</>}
                          </span>
                        </>
                      )}
                      {tx.type === "promo_credit" && (
                        <>
                          <span className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-yellow-100 text-yellow-800 mr-2">
                            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"/></svg>
                          </span>
                          <span>{tx.description || "Promo credit"}</span>
                        </>
                      )}
                      {(tx.type === "budget_refund" || tx.category === "budget_refund" || (tx.type === "credit" && tx.category === "budget_refund")) && (
                        <>
                          <span className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-green-100 text-green-800 mr-2">
                            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M3 12h18m-9-9v18"/></svg>
                          </span>
                          <span>
                            {tx.description || "Budget refunded"}
                          </span>
                        </>
                      )}
                      {/* Fallback for any unhandled transaction types */}
                      {!(tx.type === "add_funds" ||
                         tx.type === "recharge_offer" ||
                         tx.type === "spend" ||
                         (tx.type === "credit" && tx.category === "stripe_payment") ||
                         (tx.type === "credit" && tx.category === "promo_credit") ||
                         (tx.type === "debit" && tx.category === "budget_allocation") ||
                         tx.type === "promo_credit" ||
                         tx.type === "budget_refund" ||
                         tx.category === "budget_refund" ||
                         (tx.type === "credit" && tx.category === "budget_refund")) && (
                        <>
                          <span className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-gray-100 text-gray-800 mr-2">
                            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><circle cx="12" cy="12" r="10"/><path d="M12 8v4M12 16h.01"/></svg>
                          </span>
                          <span>
                            {tx.description || `${tx.type || 'Unknown'} ${tx.category ? `(${tx.category})` : ''}`}
                          </span>
                        </>
                      )}
                    </span>
                    <span className={`font-medium text-right ${
                      tx.type === "credit" ? "text-green-600" :
                      tx.type === "debit" ? "text-red-600" :
                      "text-gray-900"
                    }`}>
                      {/* Transaction amounts are stored in cents in the database, convert to dollars */}
                      {tx.type === "credit" ? "+" : tx.type === "debit" ? "-" : ""}{formatTransactionAmount(tx.amount || 0)}
                    </span>
                  </div>
                  <div className="text-sm text-muted-foreground mt-2 ml-11">
                    {tx.timestamp
                      ? tx.timestamp.toLocaleString(undefined, {
                          dateStyle: "medium",
                          timeStyle: "short",
                        })
                      : "Just now"}
                  </div>
                </li>
              ))}
            </ul>
          )}
        </CardContent>
      </Card>

      <Dialog open={showAddFunds} onOpenChange={setShowAddFunds}>
        <DialogContent className="sm:max-w-lg">
          <DialogHeader className="space-y-3">
            <DialogTitle className="flex items-center gap-3 text-xl font-semibold">
              <div className="p-3 bg-blue-50 rounded-xl">
                <CreditCard className="w-6 h-6 text-blue-600" />
              </div>
              Add Funds to Wallet
            </DialogTitle>
            <DialogDescription className="text-base text-muted-foreground">
              Securely add funds to your AdMesh wallet to power your campaigns and offers.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-6 py-4">

            {/* Amount Input */}
            <div className="space-y-3">
              <Label htmlFor="amount" className="text-sm font-semibold text-gray-900">
                Amount (USD)
              </Label>
              <div className="relative">
                <DollarSign className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-muted-foreground" />
                <Input
                  id="amount"
                  type="number"
                  min="1"
                  step="0.01"
                  placeholder="0.00"
                  value={addAmount}
                  onChange={(e) => {
                    const value = e.target.value;
                    if (value === '' || !isNaN(parseFloat(value))) {
                      setAddAmount(value);
                    }
                  }}
                  className="pl-12 pr-4 py-3 text-lg font-medium border-2 focus:border-blue-500 rounded-xl"
                />
              </div>
            </div>

            {/* Quick Amount Buttons */}
            <div className="space-y-3">
              <Label className="text-sm font-semibold text-gray-900">Quick Select</Label>
              <div className="grid grid-cols-4 gap-3">
                {[25, 50, 100, 250].map((amount) => (
                  <Button
                    key={amount}
                    variant="outline"
                    size="lg"
                    onClick={() => setAddAmount(amount.toString())}
                    className="h-12 text-sm font-medium border-2 hover:border-blue-500 hover:bg-blue-50 rounded-xl"
                  >
                    ${amount}
                  </Button>
                ))}
              </div>
            </div>

            {/* Cost Breakdown */}
            {parseFloat(addAmount) > 0 && (
              <div className="bg-gradient-to-r from-gray-50 to-gray-100 border rounded-lg p-4 space-y-3">
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Amount to add:</span>
                  <span className="font-medium">{formatCurrencyUtil(parseFloat(addAmount))}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Processing fee (3.1% + $0.30):</span>
                  <span className="font-medium">{formatCurrencyUtil(parseFloat(addAmount) * 0.031 + 0.30)}</span>
                </div>
                <div className="border-t pt-3 flex justify-between">
                  <span className="font-semibold">Total charge:</span>
                  <span className="font-semibold text-lg">
                    {formatCurrencyUtil(parseFloat(addAmount) + parseFloat(addAmount) * 0.031 + 0.30)}
                  </span>
                </div>
                <div className="flex items-center gap-2 text-xs text-muted-foreground">
                  <Shield className="w-3 h-3" />
                  <span>Powered by Stripe • Your payment is secure and encrypted</span>
                </div>
              </div>
            )}
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowAddFunds(false)}>
              Cancel
            </Button>
            <Button
              onClick={handleStripeCheckout}
              disabled={isRedirecting || !addAmount || parseFloat(addAmount) <= 0}
              className="min-w-[120px]"
            >
              {isRedirecting ? "Redirecting..." : "Checkout"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Dashboard Footer */}
      <DashboardFooter />
    </div>
  );
}
